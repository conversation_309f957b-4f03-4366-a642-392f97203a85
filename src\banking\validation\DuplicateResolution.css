/* Duplicate Resolution Styles */
.duplicate-resolution {
  max-width: 1200px;
  margin: 0 auto;
  padding: var(--spacing-lg);
}

.resolution-header {
  text-align: center;
  margin-bottom: var(--spacing-xl);
}

.resolution-header h3 {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--color-neutral-900);
  margin-bottom: var(--spacing-sm);
}

.resolution-header p {
  font-size: 1rem;
  color: var(--color-neutral-600);
  margin: 0;
}

/* Resolution Summary */
.resolution-summary {
  background: var(--color-neutral-50);
  border-radius: var(--radius-lg);
  padding: var(--spacing-lg);
  margin-bottom: var(--spacing-xl);
  border: 1px solid var(--color-neutral-200);
}

.summary-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: var(--spacing-lg);
}

.stat-item {
  text-align: center;
}

.stat-value {
  display: block;
  font-size: 2rem;
  font-weight: 700;
  color: var(--color-primary);
  margin-bottom: var(--spacing-xs);
}

.stat-label {
  font-size: 0.875rem;
  color: var(--color-neutral-600);
  font-weight: 500;
}

/* Sections */
.auto-skip-section,
.review-section {
  margin-bottom: var(--spacing-xl);
}

.auto-skip-section h4,
.review-section h4 {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--color-neutral-900);
  margin-bottom: var(--spacing-sm);
}

.auto-skip-section p,
.review-section p {
  color: var(--color-neutral-600);
  margin-bottom: var(--spacing-lg);
}

/* Bulk Actions */
.bulk-actions {
  display: flex;
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-lg);
}

.bulk-actions .btn {
  min-width: 120px;
}

/* Duplicate Matches */
.duplicate-matches {
  display: grid;
  gap: var(--spacing-lg);
}

.duplicate-match {
  background: white;
  border: 1px solid var(--color-neutral-200);
  border-radius: var(--radius-lg);
  overflow: hidden;
}

/* Match Header */
.match-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-lg);
  background: var(--color-neutral-50);
  border-bottom: 1px solid var(--color-neutral-200);
}

.score-badge {
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-full);
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
}

.score-badge.high {
  background: #FEE2E2;
  color: #DC2626;
}

.score-badge.medium {
  background: #FEF3C7;
  color: #D97706;
}

.score-badge.low {
  background: #DBEAFE;
  color: #2563EB;
}

.match-reasons {
  display: flex;
  gap: var(--spacing-xs);
  flex-wrap: wrap;
}

.reason-tag {
  padding: var(--spacing-xs) var(--spacing-sm);
  background: var(--color-neutral-200);
  border-radius: var(--radius-sm);
  font-size: 0.75rem;
  color: var(--color-neutral-700);
}

/* Transaction Comparison */
.transaction-comparison {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--spacing-lg);
  padding: var(--spacing-lg);
}

.transaction-card {
  border: 1px solid var(--color-neutral-200);
  border-radius: var(--radius-md);
  overflow: hidden;
}

.transaction-card.existing {
  border-left: 4px solid var(--color-warning);
}

.transaction-card.new {
  border-left: 4px solid var(--color-primary);
}

.transaction-card h5 {
  background: var(--color-neutral-50);
  padding: var(--spacing-md);
  margin: 0;
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--color-neutral-700);
  border-bottom: 1px solid var(--color-neutral-200);
}

.transaction-details {
  padding: var(--spacing-md);
}

.transaction-details p {
  margin: 0 0 var(--spacing-xs) 0;
  font-size: 0.875rem;
  line-height: 1.4;
}

.transaction-details p:last-child {
  margin-bottom: 0;
}

.transaction-details strong {
  color: var(--color-neutral-700);
  font-weight: 500;
  min-width: 80px;
  display: inline-block;
}

/* Resolution Controls */
.resolution-controls {
  padding: var(--spacing-lg);
  background: var(--color-neutral-25);
  border-top: 1px solid var(--color-neutral-200);
}

.resolution-options {
  display: grid;
  gap: var(--spacing-md);
}

.resolution-option {
  display: flex;
  align-items: flex-start;
  gap: var(--spacing-sm);
  padding: var(--spacing-md);
  border: 1px solid var(--color-neutral-200);
  border-radius: var(--radius-md);
  cursor: pointer;
  transition: all 0.2s ease;
}

.resolution-option:hover {
  border-color: var(--color-primary);
  background: var(--color-primary-light);
}

.resolution-option input[type="radio"] {
  margin-top: 2px;
}

.option-label {
  font-weight: 600;
  color: var(--color-neutral-900);
  display: block;
  margin-bottom: var(--spacing-xs);
}

.option-description {
  font-size: 0.875rem;
  color: var(--color-neutral-600);
  display: block;
}

/* Resolution Actions */
.resolution-actions {
  display: flex;
  justify-content: flex-end;
  gap: var(--spacing-md);
  margin-top: var(--spacing-xl);
  padding-top: var(--spacing-lg);
  border-top: 1px solid var(--color-neutral-200);
}

.resolution-actions .btn {
  min-width: 150px;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .transaction-comparison {
    grid-template-columns: 1fr;
  }
  
  .match-header {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacing-md);
  }
  
  .bulk-actions {
    flex-direction: column;
  }
  
  .resolution-actions {
    flex-direction: column;
  }
}

@media (max-width: 768px) {
  .duplicate-resolution {
    padding: var(--spacing-md);
  }
  
  .summary-stats {
    grid-template-columns: 1fr;
    gap: var(--spacing-md);
  }
  
  .resolution-option {
    padding: var(--spacing-sm);
  }
} 