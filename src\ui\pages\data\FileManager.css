/* File Manager Component Styles */

.file-manager {
  padding: 24px;
  background: #fafafa;
  min-height: 100vh;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
}

/* Header */
.file-manager-header {
  background: #ffffff;
  padding: 24px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  margin-bottom: 24px;
}

.file-manager-title-section {
  margin-bottom: 24px;
}

.file-manager-title {
  margin: 0 0 8px 0;
  font-size: 28px;
  font-weight: 700;
  color: #1d1d1f;
  letter-spacing: -0.5px;
}

.file-manager-description {
  margin: 0;
  font-size: 16px;
  color: #86868b;
  line-height: 1.5;
}

/* Statistics Cards */
.file-manager-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
}

.stat-card {
  background: #f5f5f7;
  padding: 20px;
  border-radius: 12px;
  text-align: center;
  border: 1px solid #e5e5e7;
  transition: all 0.2s ease;
}

.stat-card:hover {
  background: #ebebed;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.stat-value {
  font-size: 32px;
  font-weight: 700;
  color: #007aff;
  margin-bottom: 8px;
  font-variant-numeric: tabular-nums;
}

.stat-label {
  font-size: 14px;
  color: #86868b;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Loading State */
.file-manager-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  background: #ffffff;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid #f3f3f3;
  border-top: 3px solid #007aff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

/* No Files State */
.no-files {
  background: #ffffff;
  padding: 60px 20px;
  text-align: center;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  color: #86868b;
}

.no-files-icon {
  font-size: 64px;
  margin-bottom: 20px;
  opacity: 0.6;
}

.no-files h3 {
  margin: 0 0 12px 0;
  color: #1d1d1f;
  font-size: 24px;
  font-weight: 600;
}

.no-files p {
  margin: 0;
  font-size: 16px;
  line-height: 1.5;
}

/* Files Table */
.files-table-container {
  background: #ffffff;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  overflow: hidden;
}

.files-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 14px;
}

.files-table thead {
  background: #f5f5f7;
  border-bottom: 1px solid #d2d2d7;
}

.files-table th {
  padding: 16px 12px;
  text-align: left;
  font-weight: 600;
  color: #1d1d1f;
  font-size: 13px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  white-space: nowrap;
}

.files-table tbody tr {
  border-bottom: 1px solid #f2f2f7;
  transition: all 0.2s ease;
}

.files-table tbody tr:hover {
  background: #f9f9fb;
}

.files-table tbody tr.deleting {
  background: #fff3f3;
  opacity: 0.7;
}

.files-table td {
  padding: 16px 12px;
  vertical-align: middle;
  color: #1d1d1f;
}

/* File Info Column */
.file-name-col {
  min-width: 250px;
}

.file-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.file-icon {
  font-size: 24px;
  flex-shrink: 0;
}

.file-details {
  flex: 1;
  min-width: 0;
}

.file-name {
  font-weight: 600;
  color: #1d1d1f;
  margin-bottom: 4px;
  word-break: break-word;
}

.file-checksum {
  font-size: 12px;
  color: #86868b;
  font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
}

/* Account Column */
.account-col {
  min-width: 180px;
}

.account-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.account-name {
  font-weight: 500;
  color: #1d1d1f;
}

.account-id {
  font-size: 12px;
  color: #86868b;
  font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
}

/* Date Column */
.date-col {
  min-width: 140px;
  font-variant-numeric: tabular-nums;
  color: #424245;
}

/* Transactions Column */
.transactions-col {
  text-align: right;
  min-width: 100px;
}

.transaction-count {
  font-weight: 600;
  color: #007aff;
  font-variant-numeric: tabular-nums;
}

/* Size Column */
.size-col {
  text-align: right;
  min-width: 80px;
  font-variant-numeric: tabular-nums;
  color: #424245;
}

/* Actions Column */
.actions-col {
  text-align: center;
  min-width: 120px;
}

/* Buttons */
.btn {
  display: inline-flex;
  align-items: center;
  gap: 6px;
  padding: 10px 16px;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  text-decoration: none;
  font-family: inherit;
}

.btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.btn-sm {
  padding: 8px 12px;
  font-size: 13px;
}

.btn-danger {
  background: #ff3b30;
  color: white;
}

.btn-danger:hover:not(:disabled) {
  background: #d70015;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(255, 59, 48, 0.3);
}

.btn-warning {
  background: #ff9500;
  color: white;
}

.btn-warning:hover:not(:disabled) {
  background: #e6850e;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(255, 149, 0, 0.3);
}

.btn-secondary {
  background: #f5f5f7;
  color: #1d1d1f;
  border: 1px solid #d2d2d7;
}

.btn-secondary:hover:not(:disabled) {
  background: #ebebed;
  border-color: #b8b8bd;
}

.btn-spinner {
  width: 14px;
  height: 14px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top: 2px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

/* Modal Styles */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 20px;
}

.modal-content {
  background: white;
  border-radius: 16px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
  max-width: 500px;
  width: 100%;
  max-height: 90vh;
  overflow-y: auto;
}

.modal-header {
  padding: 24px 24px 0;
  border-bottom: 1px solid #f2f2f7;
  margin-bottom: 24px;
}

.modal-header h3 {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
  color: #1d1d1f;
  padding-bottom: 16px;
}

.modal-body {
  padding: 0 24px;
  text-align: center;
}

.warning-icon,
.danger-icon {
  font-size: 48px;
  margin-bottom: 16px;
  display: block;
}

.modal-body p {
  margin: 0 0 16px 0;
  font-size: 16px;
  line-height: 1.5;
  color: #424245;
}

.warning-text {
  color: #ff9500;
  font-weight: 500;
}

.final-warning {
  color: #ff3b30;
  font-weight: 600;
}

.deletion-details {
  text-align: left;
  background: #fff3f3;
  padding: 16px;
  border-radius: 8px;
  margin: 16px 0;
  border-left: 4px solid #ff3b30;
}

.deletion-details li {
  margin-bottom: 8px;
  color: #424245;
}

.modal-actions {
  padding: 24px;
  display: flex;
  gap: 12px;
  justify-content: flex-end;
  border-top: 1px solid #f2f2f7;
  margin-top: 24px;
}

/* Deletion Result Modal */
.status-icon {
  font-size: 48px;
  margin-bottom: 16px;
  display: block;
}

.status-icon.success {
  color: #30d158;
}

.status-icon.warning {
  color: #ff9500;
}

.deletion-stats {
  background: #f5f5f7;
  padding: 16px;
  border-radius: 8px;
  margin: 16px 0;
}

.stat-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #e5e5e7;
}

.stat-row:last-child {
  border-bottom: none;
}

.stat-row span {
  color: #86868b;
  font-size: 14px;
}

.stat-row strong {
  color: #1d1d1f;
  font-weight: 600;
}

.success-message {
  background: #e8f5e8;
  border: 1px solid #30d158;
  border-radius: 8px;
  padding: 16px;
  margin: 16px 0;
}

.success-message p {
  margin: 0 0 8px 0;
  color: #1d4ed8;
}

.success-message p:last-child {
  margin-bottom: 0;
}

.warning-message {
  background: #fff3e0;
  border: 1px solid #ff9500;
  border-radius: 8px;
  padding: 16px;
  margin: 16px 0;
}

.warning-message p {
  margin: 0 0 8px 0;
  color: #b45309;
}

.warning-message p:last-child {
  margin-bottom: 0;
}

.error-message {
  background: #fff3f3;
  border: 1px solid #ff3b30;
  border-radius: 8px;
  padding: 16px;
  margin: 16px 0;
}

.error-message p {
  margin: 0;
  color: #dc2626;
}

/* Animations */
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Responsive Design */
@media (max-width: 768px) {
  .file-manager {
    padding: 16px;
  }

  .file-manager-stats {
    grid-template-columns: 1fr;
  }

  .files-table-container {
    overflow-x: auto;
  }

  .files-table {
    min-width: 700px;
  }

  .modal-content {
    margin: 20px;
    max-width: none;
  }

  .modal-actions {
    flex-direction: column;
  }

  .btn {
    justify-content: center;
  }
}

/* Orphaned Transactions Warning */
.orphaned-warning {
  margin: 1rem 0;
  padding: 1rem;
  background: linear-gradient(135deg, #fff3cd 0%, #fcf4d6 100%);
  border: 1px solid #ffc107;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.warning-content {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
}

.warning-icon {
  font-size: 1.5rem;
  flex-shrink: 0;
  margin-top: 0.125rem;
}

.warning-message {
  flex: 1;
  color: #856404;
}

.warning-message strong {
  display: block;
  font-size: 1rem;
  margin-bottom: 0.5rem;
  color: #495057;
}

.warning-message p {
  margin: 0;
  font-size: 0.875rem;
  line-height: 1.4;
}

.warning-actions {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.btn-warning {
  background: linear-gradient(135deg, #ffc107 0%, #ffcd39 100%);
  color: #212529;
  border: 1px solid #ffc107;
  font-weight: 500;
}

.btn-warning:hover:not(:disabled) {
  background: linear-gradient(135deg, #ffcd39 0%, #ffd60a 100%);
  border-color: #ffcd39;
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(255, 193, 7, 0.3);
}

.btn-warning:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

@media (max-width: 768px) {
  .warning-content {
    flex-direction: column;
    align-items: stretch;
    gap: 0.75rem;
  }
  
  .warning-actions {
    justify-content: flex-start;
  }
} 