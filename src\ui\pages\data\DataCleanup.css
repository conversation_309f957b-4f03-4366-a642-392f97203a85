.data-cleanup {
  padding: 24px;
  background: #ffffff;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin: 16px;
  max-width: 1200px;
}

.cleanup-header {
  margin-bottom: 32px;
  text-align: center;
}

.cleanup-header h2 {
  font-size: 28px;
  font-weight: 700;
  color: #1a1a1a;
  margin: 0 0 8px 0;
}

.cleanup-header p {
  font-size: 16px;
  color: #666666;
  margin: 0;
}

/* Status Section */
.status-section {
  margin-bottom: 32px;
}

.status-card {
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  padding: 24px;
}

.status-indicator {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 16px;
}

.status-icon {
  font-size: 24px;
}

.status-text {
  font-size: 18px;
  font-weight: 600;
}

.status-loading {
  color: #3B82F6;
}

.status-warning {
  color: #F59E0B;
}

.status-clean {
  color: #10B981;
}

.status-details {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
}

.status-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: #ffffff;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
}

.status-item .label {
  font-size: 14px;
  color: #666666;
  font-weight: 500;
}

.status-item .value {
  font-size: 16px;
  font-weight: 700;
  color: #1a1a1a;
}

/* Recommendations Section */
.recommendations-section {
  margin-bottom: 32px;
}

.recommendations-section h3 {
  font-size: 20px;
  font-weight: 600;
  color: #1a1a1a;
  margin: 0 0 16px 0;
}

.recommendations-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.recommendation-item {
  padding: 12px 16px;
  background: #f0f9ff;
  border: 1px solid #bae6fd;
  border-radius: 8px;
  margin-bottom: 8px;
  font-size: 14px;
  color: #0c4a6e;
  display: flex;
  align-items: center;
  gap: 8px;
}

.recommendation-item::before {
  content: "💡";
  font-size: 16px;
}

/* Actions Section */
.actions-section {
  margin-bottom: 32px;
}

.actions-section h3 {
  font-size: 20px;
  font-weight: 600;
  color: #1a1a1a;
  margin: 0 0 20px 0;
}

.action-card {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  margin-bottom: 16px;
  background: #ffffff;
}

.action-card.emergency {
  border-color: #fecaca;
  background: #fef2f2;
}

.action-info {
  flex: 1;
  margin-right: 20px;
}

.action-info h4 {
  font-size: 16px;
  font-weight: 600;
  color: #1a1a1a;
  margin: 0 0 8px 0;
}

.action-info p {
  font-size: 14px;
  color: #666666;
  margin: 0;
  line-height: 1.5;
}

.action-card.emergency .action-info p {
  color: #dc2626;
}

/* Buttons */
.btn {
  padding: 12px 24px;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  min-width: 140px;
}

.btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.btn-primary {
  background: #3B82F6;
  color: #ffffff;
}

.btn-primary:hover:not(:disabled) {
  background: #2563EB;
}

.btn-secondary {
  background: #f3f4f6;
  color: #374151;
  border: 1px solid #d1d5db;
}

.btn-secondary:hover:not(:disabled) {
  background: #e5e7eb;
}

.btn-danger {
  background: #dc2626;
  color: #ffffff;
}

.btn-danger:hover:not(:disabled) {
  background: #b91c1c;
}

/* Report Section */
.report-section {
  margin-bottom: 32px;
}

.report-section h3 {
  font-size: 20px;
  font-weight: 600;
  color: #1a1a1a;
  margin: 0 0 20px 0;
}

.report-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 16px;
  margin-bottom: 20px;
}

.report-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border: 1px solid #e2e8f0;
  border-radius: 8px;
}

.report-label {
  font-size: 14px;
  color: #666666;
  font-weight: 500;
}

.report-value {
  font-size: 18px;
  font-weight: 700;
  color: #1a1a1a;
}

.report-value.success {
  color: #10B981;
}

.report-value.warning {
  color: #F59E0B;
}

.errors-section {
  background: #fef2f2;
  border: 1px solid #fecaca;
  border-radius: 8px;
  padding: 16px;
}

.errors-section h4 {
  font-size: 16px;
  font-weight: 600;
  color: #dc2626;
  margin: 0 0 12px 0;
}

.errors-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.error-item {
  padding: 8px 12px;
  background: #ffffff;
  border: 1px solid #fecaca;
  border-radius: 6px;
  margin-bottom: 8px;
  font-size: 14px;
  color: #dc2626;
}

/* Confirm Dialog */
.confirm-dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.confirm-dialog {
  background: #ffffff;
  border-radius: 12px;
  padding: 24px;
  max-width: 500px;
  width: 90%;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
}

.confirm-dialog h3 {
  font-size: 20px;
  font-weight: 600;
  color: #1a1a1a;
  margin: 0 0 16px 0;
}

.confirm-dialog p {
  font-size: 14px;
  color: #666666;
  line-height: 1.5;
  margin: 0 0 24px 0;
}

.dialog-actions {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
}

/* Responsive Design */
@media (max-width: 768px) {
  .data-cleanup {
    padding: 16px;
    margin: 12px;
  }
  
  .action-card {
    flex-direction: column;
    align-items: stretch;
    gap: 16px;
  }
  
  .action-info {
    margin-right: 0;
  }
  
  .report-grid {
    grid-template-columns: 1fr;
  }
  
  .status-details {
    grid-template-columns: 1fr;
  }
}

/* Force Light Mode */
.data-cleanup {
  background: #ffffff !important;
  color: #1a1a1a !important;
}

.status-card,
.action-card,
.report-item {
  background: #ffffff !important;
  border-color: #e5e5e5 !important;
}

.action-card.emergency {
  background: #fef2f2 !important;
  border-color: #fecaca !important;
}
