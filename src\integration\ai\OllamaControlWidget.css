/* COMPACT AI STATUS BAR - PROFESSIONAL DESIGN */
.ollama-control-widget {
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  border: 1px solid #cbd5e1;
  border-radius: 8px;
  padding: 12px 16px;
  margin-bottom: 16px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.ollama-control-widget:hover {
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.ai-status-bar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 16px;
}

.ai-status-left {
  display: flex;
  align-items: center;
  gap: 12px;
}

.ai-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 8px;
  color: white;
  flex-shrink: 0;
}

.ai-status-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.ai-label {
  font-size: 14px;
  font-weight: 600;
  color: #1f2937;
  line-height: 1;
}

.ai-status-badge {
  display: flex;
  align-items: center;
  gap: 8px;
}

.status-dot {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  flex-shrink: 0;
}

.status-dot.active {
  background: #10b981;
  box-shadow: 0 0 0 2px rgba(16, 185, 129, 0.2);
}

.status-dot.inactive {
  background: #ef4444;
  box-shadow: 0 0 0 2px rgba(239, 68, 68, 0.2);
}

.status-dot.loading {
  background: #3b82f6;
  animation: pulse 1.5s ease-in-out infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

.status-text {
  font-size: 12px;
  font-weight: 500;
  color: #4b5563;
}

.model-name {
  font-size: 11px;
  color: #6b7280;
  font-style: italic;
}

.ai-controls {
  display: flex;
  align-items: center;
  gap: 8px;
}

.ai-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 28px;
  height: 28px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 12px;
}

.ai-btn:hover {
  transform: scale(1.05);
}

.ai-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
}

.start-btn {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  color: white;
}

.start-btn:hover:not(:disabled) {
  background: linear-gradient(135deg, #059669 0%, #047857 100%);
}

.stop-btn {
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
  color: white;
}

.stop-btn:hover:not(:disabled) {
  background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
}

.refresh-btn {
  background: linear-gradient(135deg, #6b7280 0%, #4b5563 100%);
  color: white;
}

.refresh-btn:hover:not(:disabled) {
  background: linear-gradient(135deg, #4b5563 0%, #374151 100%);
}

/* ERROR NOTICE - COMPACT */
.ai-error-notice {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-top: 8px;
  padding: 8px 12px;
  background: #fef2f2;
  border: 1px solid #fecaca;
  border-radius: 6px;
  font-size: 12px;
}

.error-icon {
  color: #ef4444;
  font-size: 14px;
  flex-shrink: 0;
}

.error-text {
  flex: 1;
  color: #7f1d1d;
  font-weight: 500;
}

.error-dismiss {
  background: none;
  border: none;
  color: #9ca3af;
  cursor: pointer;
  font-size: 16px;
  padding: 0;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.error-dismiss:hover {
  background: #f3f4f6;
  color: #4b5563;
}

/* CONFIRMATION DIALOG - MODERN */
.confirmation-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(4px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  animation: fadeIn 0.2s ease;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

.confirmation-dialog {
  background: white;
  border-radius: 16px;
  padding: 24px;
  max-width: 400px;
  width: 90%;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  animation: slideIn 0.3s ease;
}

@keyframes slideIn {
  from { transform: scale(0.9) translateY(-20px); opacity: 0; }
  to { transform: scale(1) translateY(0); opacity: 1; }
}

.dialog-header h3 {
  margin: 0 0 16px 0;
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
}

.dialog-content p {
  margin: 0 0 16px 0;
  color: #4b5563;
  line-height: 1.5;
}

.safety-notice {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  background: #f0f9ff;
  border: 1px solid #bae6fd;
  border-radius: 8px;
  font-size: 12px;
  color: #0c4a6e;
}

.safety-icon {
  font-size: 14px;
}

.dialog-actions {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
  margin-top: 20px;
}

.dialog-button {
  padding: 8px 16px;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.cancel-button {
  background: #f3f4f6;
  color: #4b5563;
}

.cancel-button:hover {
  background: #e5e7eb;
}

.confirm-button {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  color: white;
}

.confirm-button:hover {
  background: linear-gradient(135deg, #1d4ed8 0%, #1e40af 100%);
}

/* RESPONSIVE */
@media (max-width: 768px) {
  .ai-status-bar {
    flex-direction: column;
    gap: 12px;
    align-items: flex-start;
  }
  
  .ai-controls {
    align-self: flex-end;
  }
  
  .ai-error-notice {
    flex-direction: column;
    align-items: flex-start;
    gap: 6px;
  }
  
  .error-dismiss {
    align-self: flex-end;
  }
} 