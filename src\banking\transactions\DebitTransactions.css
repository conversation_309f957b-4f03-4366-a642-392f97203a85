/* DEBIT TRANSACTIONS STYLES */

.debit-transactions-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 20px;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* Loading State */
.debit-transactions-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 300px;
  gap: 15px;
}

.loading-spinner {
  font-size: 2rem;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* Header Section */
.debit-transactions-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 30px;
  border-radius: 12px;
  margin-bottom: 25px;
  box-shadow: 0 8px 16px rgba(0,0,0,0.1);
}

.debit-transactions-header h2 {
  margin: 0 0 10px 0;
  font-size: 2.2rem;
  font-weight: 700;
}

.debit-transactions-header p {
  margin: 0;
  opacity: 0.9;
  font-size: 1.1rem;
}

/* Summary Cards */
.summary-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin-top: 25px;
}

.summary-card {
  background: rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 10px;
  padding: 20px;
  text-align: center;
  transition: transform 0.2s ease;
}

.summary-card:hover {
  transform: translateY(-2px);
}

.summary-card h4 {
  margin: 0 0 10px 0;
  font-size: 0.9rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  opacity: 0.9;
}

.summary-value {
  font-size: 2rem;
  font-weight: 700;
  display: block;
}

.summary-value.pending {
  color: #ffd54f;
}

.summary-value.matched {
  color: #a5d6a7;
}

.summary-value.confirmed {
  color: #c8e6c9;
}

/* Filters Section */
.filters-section {
  background: white;
  border-radius: 10px;
  padding: 25px;
  margin-bottom: 25px;
  box-shadow: 0 4px 8px rgba(0,0,0,0.08);
  border: 1px solid #e0e0e0;
}

.filters-section h3 {
  margin: 0 0 20px 0;
  color: #333;
  font-size: 1.3rem;
}

.filters-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
}

.filter-group {
  display: flex;
  flex-direction: column;
}

.filter-group label {
  margin-bottom: 8px;
  font-weight: 600;
  color: #555;
  font-size: 0.9rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.filter-group select,
.filter-group input {
  padding: 12px;
  border: 2px solid #e0e0e0;
  border-radius: 8px;
  font-size: 1rem;
  transition: border-color 0.2s ease;
  background: white;
}

.filter-group select:focus,
.filter-group input:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

/* Transactions Table Section */
.transactions-table-section {
  background: white;
  border-radius: 10px;
  padding: 25px;
  box-shadow: 0 4px 8px rgba(0,0,0,0.08);
  border: 1px solid #e0e0e0;
}

.transactions-table-section h3 {
  margin: 0 0 20px 0;
  color: #333;
  font-size: 1.3rem;
}

.no-transactions {
  text-align: center;
  padding: 60px 20px;
  color: #666;
}

.no-transactions p {
  margin: 10px 0;
  font-size: 1.1rem;
}

/* Transactions Table */
.transactions-table-container {
  overflow-x: auto;
  border-radius: 8px;
  border: 1px solid #e0e0e0;
}

.transactions-table {
  width: 100%;
  border-collapse: collapse;
  background: white;
}

.transactions-table th {
  background: #f8f9fa;
  padding: 15px 12px;
  text-align: left;
  font-weight: 600;
  color: #333;
  border-bottom: 2px solid #e0e0e0;
  font-size: 0.9rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.transactions-table td {
  padding: 15px 12px;
  border-bottom: 1px solid #f0f0f0;
  vertical-align: top;
}

.transaction-row {
  transition: background-color 0.2s ease;
}

.transaction-row:hover {
  background-color: #f8f9fa;
}

.transaction-row.pending {
  border-left: 4px solid #ff9800;
}

.transaction-row.auto_matched {
  border-left: 4px solid #4caf50;
}

.transaction-row.manually_matched {
  border-left: 4px solid #2196f3;
}

.transaction-row.confirmed {
  border-left: 4px solid #8bc34a;
}

/* Transaction Description */
.transaction-description {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.description-text {
  font-weight: 500;
  color: #333;
}

.reference-text {
  color: #666;
  font-size: 0.85rem;
}

/* Amount Cell */
.amount-cell {
  text-align: right;
  font-weight: 600;
}

.debit-amount {
  color: #d32f2f;
  font-family: 'Courier New', monospace;
}

/* Category Badge */
.category-badge {
  display: inline-block;
  padding: 4px 8px;
  background: #e3f2fd;
  color: #1976d2;
  border-radius: 4px;
  font-size: 0.8rem;
  font-weight: 500;
}

/* Status Badge */
.status-badge {
  display: inline-block;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: bold;
}

/* Confidence Score */
.confidence-score {
  font-weight: 600;
  padding: 2px 6px;
  border-radius: 3px;
}

.confidence-score.high {
  background: #e8f5e8;
  color: #2e7d32;
}

.confidence-score.medium {
  background: #fff3e0;
  color: #ef6c00;
}

.confidence-score.low {
  background: #ffebee;
  color: #c62828;
}

.no-confidence {
  color: #999;
  font-style: italic;
}

/* Matched Entity */
.matched-entity {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.matched-entity strong {
  color: #333;
  font-size: 0.9rem;
}

.matched-entity small {
  color: #666;
  font-size: 0.8rem;
}

.no-match {
  color: #999;
  font-style: italic;
  font-size: 0.9rem;
}

/* Action Buttons */
.action-buttons {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.action-buttons button {
  padding: 6px 12px;
  border: none;
  border-radius: 4px;
  font-size: 0.8rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.btn-auto-reconcile {
  background: #e3f2fd;
  color: #1976d2;
}

.btn-auto-reconcile:hover {
  background: #bbdefb;
}

.btn-manual-reconcile {
  background: #f3e5f5;
  color: #7b1fa2;
}

.btn-manual-reconcile:hover {
  background: #e1bee7;
}

.btn-confirm {
  background: #e8f5e8;
  color: #2e7d32;
}

.btn-confirm:hover {
  background: #c8e6c9;
}

.verified-badge {
  background: #e8f5e8;
  color: #2e7d32;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 0.8rem;
  font-weight: 500;
  cursor: help;
}

/* Modal Styles */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 20px;
}

.modal-content {
  background: white;
  border-radius: 12px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
  max-width: 800px;
  width: 100%;
  max-height: 80vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 25px;
  border-bottom: 1px solid #e0e0e0;
  background: #f8f9fa;
}

.modal-header h3 {
  margin: 0;
  color: #333;
  font-size: 1.3rem;
}

.close-button {
  background: none;
  border: none;
  font-size: 1.5rem;
  color: #666;
  cursor: pointer;
  padding: 5px;
  border-radius: 4px;
  transition: background-color 0.2s ease;
}

.close-button:hover {
  background: #e0e0e0;
}

.modal-body {
  padding: 25px;
  overflow-y: auto;
  flex: 1;
}

.transaction-summary {
  background: #f8f9fa;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 20px;
}

.transaction-summary h4 {
  margin: 0 0 15px 0;
  color: #333;
}

.transaction-details p {
  margin: 8px 0;
  color: #555;
}

.match-options {
  margin-bottom: 20px;
}

.tabs {
  display: flex;
  border-bottom: 1px solid #e0e0e0;
  margin-bottom: 20px;
}

.tabs button {
  background: none;
  border: none;
  padding: 12px 20px;
  cursor: pointer;
  border-bottom: 2px solid transparent;
  font-weight: 500;
  color: #666;
  transition: all 0.2s ease;
}

.tabs button.active {
  color: #667eea;
  border-bottom-color: #667eea;
}

.tabs button:hover {
  background: #f8f9fa;
}

.tab-content {
  max-height: 300px;
  overflow-y: auto;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
}

.loading-options {
  padding: 40px;
  text-align: center;
  color: #666;
}

.option-item {
  padding: 15px;
  border-bottom: 1px solid #f0f0f0;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.option-item:hover {
  background: #f8f9fa;
}

.option-item.selected {
  background: #e3f2fd;
  border-left: 4px solid #1976d2;
}

.option-header {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 8px;
}

.option-header strong {
  flex: 1;
  color: #333;
}

.option-header .amount {
  font-weight: 600;
  color: #d32f2f;
  font-family: 'Courier New', monospace;
}

.option-details {
  margin-left: 30px;
  color: #666;
  font-size: 0.9rem;
}

.option-details p {
  margin: 4px 0;
}

.notes-section {
  margin-top: 20px;
}

.notes-section label {
  display: block;
  margin-bottom: 8px;
  font-weight: 600;
  color: #333;
}

.notes-section textarea {
  width: 100%;
  padding: 12px;
  border: 2px solid #e0e0e0;
  border-radius: 8px;
  font-family: inherit;
  font-size: 1rem;
  resize: vertical;
  transition: border-color 0.2s ease;
}

.notes-section textarea:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 15px;
  padding: 20px 25px;
  border-top: 1px solid #e0e0e0;
  background: #f8f9fa;
}

.modal-footer button {
  padding: 10px 20px;
  border: none;
  border-radius: 6px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.btn-cancel {
  background: #f5f5f5;
  color: #666;
}

.btn-cancel:hover {
  background: #e0e0e0;
}

.btn-confirm {
  background: #667eea;
  color: white;
}

.btn-confirm:hover {
  background: #5a6fd8;
}

.btn-confirm:disabled {
  background: #ccc;
  cursor: not-allowed;
}

/* Responsive Design */
@media (max-width: 768px) {
  .debit-transactions-container {
    padding: 15px;
  }
  
  .debit-transactions-header {
    padding: 20px;
  }
  
  .debit-transactions-header h2 {
    font-size: 1.8rem;
  }
  
  .summary-cards {
    grid-template-columns: repeat(2, 1fr);
    gap: 15px;
  }
  
  .filters-grid {
    grid-template-columns: 1fr;
    gap: 15px;
  }
  
  .transactions-table th,
  .transactions-table td {
    padding: 10px 8px;
    font-size: 0.9rem;
  }
  
  .action-buttons {
    flex-direction: column;
    gap: 5px;
  }
  
  .action-buttons button {
    font-size: 0.7rem;
    padding: 4px 8px;
  }
  
  .modal-overlay {
    padding: 10px;
  }
  
  .modal-content {
    max-height: 90vh;
  }
  
  .modal-header,
  .modal-body,
  .modal-footer {
    padding: 15px 20px;
  }
}

@media (max-width: 480px) {
  .summary-cards {
    grid-template-columns: 1fr;
  }
  
  .transactions-table-container {
    font-size: 0.8rem;
  }
  
  .option-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 5px;
  }
} 