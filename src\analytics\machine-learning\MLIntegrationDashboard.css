/* ML Integration Dashboard Styles */
.ml-integration-dashboard {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
}

.ml-dashboard-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 60vh;
  color: white;
  text-align: center;
}

.loading-spinner {
  width: 60px;
  height: 60px;
  border: 4px solid rgba(255, 255, 255, 0.3);
  border-top: 4px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Dashboard Header */
.dashboard-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  background: rgba(255, 255, 255, 0.1);
  padding: 20px;
  border-radius: 15px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.dashboard-header h1 {
  color: white;
  margin: 0;
  font-size: 2.2rem;
  font-weight: 700;
  display: flex;
  align-items: center;
  gap: 15px;
}

.tf-version {
  background: rgba(255, 255, 255, 0.2);
  padding: 5px 12px;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 500;
}

.header-actions {
  display: flex;
  gap: 15px;
}

.header-actions button {
  background: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
  color: white;
  padding: 12px 20px;
  border-radius: 10px;
  cursor: pointer;
  font-weight: 600;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.header-actions button:hover:not(:disabled) {
  background: rgba(255, 255, 255, 0.3);
  transform: translateY(-2px);
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.2);
}

.header-actions button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* Dashboard Tabs */
.dashboard-tabs {
  display: flex;
  gap: 5px;
  margin-bottom: 30px;
  background: rgba(255, 255, 255, 0.1);
  padding: 8px;
  border-radius: 15px;
  backdrop-filter: blur(10px);
}

.tab {
  flex: 1;
  background: transparent;
  border: none;
  color: rgba(255, 255, 255, 0.7);
  padding: 15px 20px;
  border-radius: 10px;
  cursor: pointer;
  font-weight: 600;
  transition: all 0.3s ease;
  text-align: center;
}

.tab:hover {
  background: rgba(255, 255, 255, 0.1);
  color: white;
}

.tab.active {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

/* Dashboard Content */
.dashboard-content {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20px;
  padding: 30px;
  min-height: 600px;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
}

/* Overview Section */
.overview-section {
  display: flex;
  flex-direction: column;
  gap: 30px;
}

.system-health-card {
  background: linear-gradient(135deg, #4CAF50, #45a049);
  color: white;
  padding: 30px;
  border-radius: 15px;
  text-align: center;
  box-shadow: 0 5px 20px rgba(76, 175, 80, 0.3);
}

.system-health-card h2 {
  margin: 0 0 20px 0;
  font-size: 1.8rem;
}

.health-indicator {
  display: inline-block;
  padding: 10px 25px;
  border-radius: 25px;
  font-weight: 700;
  font-size: 1.1rem;
  margin-bottom: 20px;
  background: rgba(255, 255, 255, 0.2);
}

.health-metrics {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin-top: 20px;
}

.metric {
  background: rgba(255, 255, 255, 0.1);
  padding: 15px;
  border-radius: 10px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.metric-label {
  font-weight: 500;
}

.metric-value {
  font-weight: 700;
  font-size: 1.2rem;
}

/* Models Grid */
.models-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 25px;
}

.model-card {
  background: white;
  border-radius: 15px;
  padding: 25px;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(0, 0, 0, 0.05);
}

.model-card h3 {
  margin: 0 0 20px 0;
  color: #333;
  font-size: 1.3rem;
  display: flex;
  align-items: center;
  gap: 10px;
}

.model-status {
  margin-bottom: 20px;
}

.status-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.status-active {
  color: #4CAF50;
  font-weight: 600;
}

.status-inactive {
  color: #f44336;
  font-weight: 600;
}

.model-stats {
  background: #f8f9fa;
  padding: 15px;
  border-radius: 10px;
}

.stat {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
}

.stat:last-child {
  margin-bottom: 0;
}

/* Insights Section */
.insights-section {
  max-width: 100%;
}

.insights-section h2 {
  margin: 0 0 25px 0;
  color: #333;
  font-size: 2rem;
}

.no-insights {
  text-align: center;
  padding: 60px 20px;
  color: #666;
  font-size: 1.1rem;
}

.insights-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 25px;
}

.insight-card {
  background: white;
  border-radius: 15px;
  padding: 25px;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.08);
  border-left: 5px solid #2196F3;
}

.insight-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.insight-type {
  font-weight: 700;
  color: #333;
  font-size: 0.9rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.insight-impact {
  padding: 5px 12px;
  border-radius: 12px;
  color: white;
  font-weight: 600;
  font-size: 0.8rem;
  text-transform: uppercase;
}

.insight-prediction {
  font-size: 1.1rem;
  color: #333;
  margin-bottom: 15px;
  line-height: 1.5;
}

.insight-metadata {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 10px;
  margin-bottom: 20px;
  padding: 15px;
  background: #f8f9fa;
  border-radius: 10px;
}

.metadata-row {
  display: flex;
  justify-content: space-between;
}

.insight-recommendations {
  margin-top: 20px;
}

.insight-recommendations h4 {
  margin: 0 0 10px 0;
  color: #333;
  font-size: 1rem;
}

.insight-recommendations ul {
  margin: 0;
  padding-left: 20px;
}

.insight-recommendations li {
  margin-bottom: 5px;
  color: #555;
  line-height: 1.4;
}

/* Analysis Section */
.analysis-section h2 {
  margin: 0 0 25px 0;
  color: #333;
  font-size: 2rem;
}

.analysis-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(500px, 1fr));
  gap: 25px;
}

.analysis-card {
  background: white;
  border-radius: 15px;
  padding: 25px;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.08);
}

.transaction-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 2px solid #f0f0f0;
}

.transaction-desc {
  font-weight: 600;
  color: #333;
  flex: 1;
  margin-right: 20px;
}

.transaction-amount {
  font-weight: 700;
  font-size: 1.2rem;
  color: #2196F3;
}

.analysis-results {
  display: grid;
  gap: 20px;
}

.result-section {
  background: #f8f9fa;
  padding: 20px;
  border-radius: 10px;
}

.result-section h4 {
  margin: 0 0 15px 0;
  color: #333;
  font-size: 1.1rem;
}

.result-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
}

.result-reasoning {
  margin-top: 10px;
  padding: 10px;
  background: rgba(33, 150, 243, 0.1);
  border-radius: 8px;
  font-size: 0.9rem;
  color: #555;
  font-style: italic;
}

.result-error {
  color: #f44336;
  font-style: italic;
}

.sentiment-positive { color: #4CAF50; font-weight: 600; }
.sentiment-neutral { color: #FF9800; font-weight: 600; }
.sentiment-negative { color: #f44336; font-weight: 600; }

.topics {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.topic-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 5px;
}

.topic-tag {
  background: #e3f2fd;
  color: #1976d2;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 500;
}

.btn-analyze-detail {
  width: 100%;
  background: linear-gradient(135deg, #2196F3, #1976D2);
  color: white;
  border: none;
  padding: 12px;
  border-radius: 10px;
  font-weight: 600;
  cursor: pointer;
  margin-top: 20px;
  transition: all 0.3s ease;
}

.btn-analyze-detail:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 20px rgba(33, 150, 243, 0.3);
}

/* Performance Section */
.performance-section h2 {
  margin: 0 0 25px 0;
  color: #333;
  font-size: 2rem;
}

.metrics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 25px;
  margin-bottom: 40px;
}

.metric-card {
  background: white;
  border-radius: 15px;
  padding: 25px;
  text-align: center;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.08);
  transition: transform 0.3s ease;
}

.metric-card:hover {
  transform: translateY(-5px);
}

.metric-icon {
  font-size: 2.5rem;
  margin-bottom: 15px;
}

.metric-card .metric-label {
  font-weight: 600;
  color: #666;
  margin-bottom: 10px;
  font-size: 0.9rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.metric-card .metric-value {
  font-size: 2.5rem;
  font-weight: 700;
  color: #333;
  margin-bottom: 10px;
}

.metric-description {
  font-size: 0.9rem;
  color: #666;
}

.tensorflow-info {
  background: linear-gradient(135deg, #FF6F00, #FF8F00);
  color: white;
  padding: 25px;
  border-radius: 15px;
}

.tensorflow-info h3 {
  margin: 0 0 20px 0;
  font-size: 1.5rem;
}

.memory-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 15px;
}

.memory-stat {
  background: rgba(255, 255, 255, 0.1);
  padding: 15px;
  border-radius: 10px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

/* Testing Section */
.testing-section h2 {
  margin: 0 0 25px 0;
  color: #333;
  font-size: 2rem;
}

.test-controls {
  margin-bottom: 30px;
  text-align: center;
}

.btn-run-test {
  background: linear-gradient(135deg, #4CAF50, #45a049);
  color: white;
  border: none;
  padding: 15px 30px;
  border-radius: 10px;
  font-weight: 600;
  font-size: 1.1rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn-run-test:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 5px 20px rgba(76, 175, 80, 0.3);
}

.btn-run-test:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.test-results {
  display: grid;
  gap: 25px;
}

.test-result-card {
  background: white;
  border-radius: 15px;
  padding: 25px;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.08);
}

.test-result-card h3 {
  margin: 0 0 20px 0;
  color: #333;
  font-size: 1.3rem;
}

.test-info, .performance-stats {
  display: grid;
  gap: 10px;
}

.test-row, .stat-row {
  display: flex;
  justify-content: space-between;
  padding: 10px 0;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.test-success {
  color: #4CAF50;
  font-weight: 600;
}

.test-failure {
  color: #f44336;
  font-weight: 600;
}

.retrain-section {
  background: #f8f9fa;
  padding: 25px;
  border-radius: 15px;
  margin-top: 30px;
  text-align: center;
}

.retrain-section h3 {
  margin: 0 0 15px 0;
  color: #333;
}

.retrain-section p {
  margin: 0 0 20px 0;
  color: #666;
}

.btn-retrain-models {
  background: linear-gradient(135deg, #9C27B0, #7B1FA2);
  color: white;
  border: none;
  padding: 12px 25px;
  border-radius: 10px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn-retrain-models:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 5px 20px rgba(156, 39, 176, 0.3);
}

.btn-retrain-models:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* Transaction Detail Modal */
.transaction-detail-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  backdrop-filter: blur(5px);
}

.modal-content {
  background: white;
  border-radius: 20px;
  padding: 0;
  max-width: 600px;
  width: 90%;
  max-height: 80vh;
  overflow: hidden;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
}

.modal-header {
  background: linear-gradient(135deg, #2196F3, #1976D2);
  color: white;
  padding: 20px 30px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.modal-header h3 {
  margin: 0;
  font-size: 1.5rem;
}

.modal-close {
  background: none;
  border: none;
  color: white;
  font-size: 2rem;
  cursor: pointer;
  padding: 0;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: background 0.3s ease;
}

.modal-close:hover {
  background: rgba(255, 255, 255, 0.2);
}

.modal-body {
  padding: 30px;
  overflow-y: auto;
  max-height: calc(80vh - 80px);
}

.transaction-details h4 {
  margin: 0 0 15px 0;
  color: #333;
}

.detail-row {
  display: flex;
  justify-content: space-between;
  padding: 10px 0;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.detail-row:last-child {
  border-bottom: none;
}

/* Responsive Design */
@media (max-width: 768px) {
  .ml-integration-dashboard {
    padding: 15px;
  }
  
  .dashboard-header {
    flex-direction: column;
    gap: 20px;
    text-align: center;
  }
  
  .dashboard-header h1 {
    font-size: 1.8rem;
  }
  
  .header-actions {
    flex-wrap: wrap;
    justify-content: center;
  }
  
  .dashboard-tabs {
    flex-direction: column;
  }
  
  .models-grid,
  .insights-grid,
  .analysis-grid {
    grid-template-columns: 1fr;
  }
  
  .metrics-grid {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .metric-card .metric-value {
    font-size: 2rem;
  }
  
  .modal-content {
    width: 95%;
    margin: 20px;
  }
}

@media (max-width: 480px) {
  .metrics-grid {
    grid-template-columns: 1fr;
  }
  
  .insight-metadata {
    grid-template-columns: 1fr;
  }
  
  .memory-stats {
    grid-template-columns: 1fr;
  }
} 