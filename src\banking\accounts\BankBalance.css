/* Bank Balance Component Styles - Professional Gray Theme */
.bank-balance {
  background: var(--color-neutral-50);
  min-height: 100vh;
  padding: var(--spacing-xl);
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
}

/* Header */
.bank-balance-header {
  text-align: center;
  margin-bottom: var(--spacing-2xl);
}

.bank-balance-header h2 {
  font-size: 2.25rem;
  font-weight: 700;
  color: var(--color-neutral-900);
  margin: 0 0 var(--spacing-sm) 0;
}

.bank-balance-header p {
  font-size: 1.125rem;
  color: var(--color-neutral-600);
  margin: 0;
}

/* Loading State */
.bank-balance-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  text-align: center;
}

.loading-spinner {
  width: 48px;
  height: 48px;
  border: 4px solid var(--color-neutral-200);
  border-top: 4px solid var(--color-neutral-600);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: var(--spacing-lg);
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.bank-balance-loading p {
  font-size: 1.125rem;
  color: var(--color-neutral-600);
  margin: 0;
}

/* Error State */
.bank-balance-error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  text-align: center;
  padding: var(--spacing-xl);
}

.bank-balance-error h3 {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--color-red-600);
  margin: 0 0 var(--spacing-md) 0;
}

.bank-balance-error p {
  font-size: 1rem;
  color: var(--color-neutral-600);
  margin: 0 0 var(--spacing-lg) 0;
  max-width: 400px;
}

.retry-button {
  background: var(--color-neutral-900);
  color: white;
  border: none;
  border-radius: 8px;
  padding: var(--spacing-md) var(--spacing-lg);
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.retry-button:hover {
  background: var(--color-neutral-700);
  transform: translateY(-1px);
}

/* Statistics Cards */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--spacing-lg);
  margin-bottom: var(--spacing-2xl);
}

.stat-card {
  background: white;
  border: 1px solid var(--color-neutral-200);
  border-radius: 12px;
  padding: var(--spacing-lg);
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  transition: all 0.2s ease;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.stat-card:hover {
  border-color: var(--color-neutral-300);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transform: translateY(-1px);
}

.stat-card.warning {
  border-color: var(--color-orange-300);
  background: #fef3cd;
}

.stat-card.warning:hover {
  border-color: var(--color-orange-400);
  background: #fed7aa;
}

.stat-icon {
  width: 48px;
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-radius: 12px;
  font-size: 20px;
  flex-shrink: 0;
}

.stat-content {
  flex: 1;
}

.stat-value {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--color-neutral-900);
  line-height: 1.2;
}

.stat-label {
  font-size: 0.875rem;
  color: var(--color-neutral-600);
  font-weight: 500;
  margin-top: 2px;
}

/* Controls Section */
.controls-section {
  background: white;
  border: 1px solid var(--color-neutral-200);
  border-radius: 12px;
  padding: var(--spacing-xl);
  margin-bottom: var(--spacing-xl);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.filters-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--spacing-lg);
  margin-bottom: var(--spacing-lg);
}

.filter-group {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

.filter-group label {
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--color-neutral-700);
}

.filter-group select,
.filter-group input {
  padding: var(--spacing-md);
  border: 1px solid var(--color-neutral-300);
  border-radius: 8px;
  font-size: 0.875rem;
  font-family: inherit;
  background: white;
  transition: all 0.2s ease;
}

.filter-group select:focus,
.filter-group input:focus {
  outline: none;
  border-color: var(--color-neutral-600);
  box-shadow: 0 0 0 3px rgba(156, 163, 175, 0.1);
}

.controls-actions {
  display: flex;
  gap: var(--spacing-md);
  justify-content: flex-end;
  padding-top: var(--spacing-lg);
  border-top: 1px solid var(--color-neutral-200);
}

.clear-filters-button,
.export-button {
  padding: var(--spacing-md) var(--spacing-lg);
  border-radius: 8px;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  border: none;
}

.clear-filters-button {
  background: var(--color-neutral-100);
  color: var(--color-neutral-700);
  border: 1px solid var(--color-neutral-300);
}

.clear-filters-button:hover {
  background: var(--color-neutral-200);
  border-color: var(--color-neutral-400);
}

.export-button {
  background: var(--color-neutral-900);
  color: white;
}

.export-button:hover {
  background: var(--color-neutral-700);
  transform: translateY(-1px);
}

/* Results Info */
.results-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-lg);
  padding: 0 var(--spacing-sm);
}

.results-info span {
  font-size: 0.875rem;
  color: var(--color-neutral-600);
  font-weight: 500;
}

.pagination-controls label {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  font-size: 0.875rem;
  color: var(--color-neutral-600);
  font-weight: 500;
}

.pagination-controls select {
  padding: var(--spacing-sm);
  border: 1px solid var(--color-neutral-300);
  border-radius: 6px;
  font-size: 0.875rem;
  background: white;
}

/* Table Container */
.table-container {
  background: white;
  border: 1px solid var(--color-neutral-200);
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  margin-bottom: var(--spacing-xl);
}

/* Balance Table */
.balance-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 0.875rem;
}

.balance-table thead {
  background: var(--color-neutral-50);
  border-bottom: 1px solid var(--color-neutral-200);
}

.balance-table th {
  padding: var(--spacing-lg);
  text-align: left;
  font-weight: 600;
  color: var(--color-neutral-700);
  border-right: 1px solid var(--color-neutral-200);
  position: relative;
}

.balance-table th:last-child {
  border-right: none;
}

.balance-table th.sortable {
  cursor: pointer;
  user-select: none;
  transition: all 0.2s ease;
}

.balance-table th.sortable:hover {
  background: var(--color-neutral-100);
  color: var(--color-neutral-900);
}

.balance-table tbody tr {
  border-bottom: 1px solid var(--color-neutral-100);
  transition: all 0.2s ease;
}

.balance-table tbody tr:hover {
  background: var(--color-neutral-50);
}

.balance-table tbody tr:last-child {
  border-bottom: none;
}

.balance-table tbody tr.has-duplicates {
  background: #fef3cd;
}

.balance-table tbody tr.has-duplicates:hover {
  background: #fed7aa;
}

.balance-table td {
  padding: var(--spacing-lg);
  border-right: 1px solid var(--color-neutral-100);
  vertical-align: top;
}

.balance-table td:last-child {
  border-right: none;
}

.date-cell {
  font-weight: 500;
  color: var(--color-neutral-900);
  white-space: nowrap;
}

.account-cell {
  font-weight: 500;
  color: var(--color-neutral-900);
}

.currency-cell {
  text-align: right;
  font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
  font-weight: 500;
  white-space: nowrap;
}

.currency-cell.movement-positive {
  color: var(--color-green-600);
}

.currency-cell.movement-negative {
  color: var(--color-red-600);
}

.currency-cell.movement-neutral {
  color: var(--color-neutral-600);
}

.count-cell {
  text-align: center;
  font-weight: 500;
  color: var(--color-neutral-700);
}

.time-cell {
  text-align: center;
  font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
  color: var(--color-neutral-600);
  font-size: 0.8125rem;
}

/* Duplicate Warning Styles */
.duplicate-warning {
  margin-left: var(--spacing-sm);
  font-size: 0.875rem;
  cursor: help;
}

.duplicate-info {
  margin-left: var(--spacing-xs);
  color: var(--color-orange-600);
  font-weight: bold;
  cursor: help;
}

/* Pagination */
.pagination {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-xl);
}

.pagination-button {
  padding: var(--spacing-md) var(--spacing-lg);
  border: 1px solid var(--color-neutral-300);
  background: white;
  color: var(--color-neutral-700);
  border-radius: 8px;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.pagination-button:hover:not(:disabled) {
  background: var(--color-neutral-50);
  border-color: var(--color-neutral-400);
}

.pagination-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.pagination-info {
  font-size: 0.875rem;
  color: var(--color-neutral-600);
  font-weight: 500;
  margin: 0 var(--spacing-md);
}

.pagination-numbers {
  display: flex;
  gap: var(--spacing-xs);
}

.pagination-number {
  padding: var(--spacing-sm) var(--spacing-md);
  border: 1px solid var(--color-neutral-300);
  background: white;
  color: var(--color-neutral-700);
  border-radius: 6px;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  min-width: 36px;
  text-align: center;
}

.pagination-number:hover {
  background: var(--color-neutral-50);
  border-color: var(--color-neutral-400);
}

.pagination-number.active {
  background: var(--color-neutral-900);
  color: white;
  border-color: var(--color-neutral-900);
}

/* No Results */
.no-results {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-3xl);
  text-align: center;
  background: white;
  border: 1px solid var(--color-neutral-200);
  border-radius: 12px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.no-results-icon {
  font-size: 4rem;
  margin-bottom: var(--spacing-lg);
  opacity: 0.5;
}

.no-results h3 {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--color-neutral-900);
  margin: 0 0 var(--spacing-md) 0;
}

.no-results p {
  font-size: 1rem;
  color: var(--color-neutral-600);
  margin: 0;
  max-width: 400px;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .bank-balance {
    padding: var(--spacing-lg);
  }
  
  .stats-grid {
    grid-template-columns: repeat(auto-fit, minmax(160px, 1fr));
    gap: var(--spacing-md);
  }
  
  .filters-grid {
    grid-template-columns: repeat(auto-fit, minmax(160px, 1fr));
    gap: var(--spacing-md);
  }
  
  .controls-actions {
    flex-direction: column;
  }
}

@media (max-width: 768px) {
  .bank-balance {
    padding: var(--spacing-md);
  }
  
  .bank-balance-header h2 {
    font-size: 1.875rem;
  }
  
  .bank-balance-header p {
    font-size: 1rem;
  }
  
  .stats-grid {
    grid-template-columns: 1fr;
  }
  
  .stat-card {
    padding: var(--spacing-md);
  }
  
  .stat-icon {
    width: 40px;
    height: 40px;
    font-size: 18px;
  }
  
  .stat-value {
    font-size: 1.25rem;
  }
  
  .filters-grid {
    grid-template-columns: 1fr;
  }
  
  .results-info {
    flex-direction: column;
    gap: var(--spacing-md);
    align-items: flex-start;
  }
  
  .table-container {
    overflow-x: auto;
  }
  
  .balance-table {
    min-width: 700px;
  }
  
  .balance-table th,
  .balance-table td {
    padding: var(--spacing-md);
  }
  
  .pagination {
    flex-wrap: wrap;
    gap: var(--spacing-sm);
  }
  
  .pagination-numbers {
    order: -1;
    width: 100%;
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .bank-balance {
    padding: var(--spacing-sm);
  }
  
  .controls-section,
  .table-container {
    margin-left: calc(-1 * var(--spacing-sm));
    margin-right: calc(-1 * var(--spacing-sm));
    border-radius: 0;
    border-left: none;
    border-right: none;
  }
} 